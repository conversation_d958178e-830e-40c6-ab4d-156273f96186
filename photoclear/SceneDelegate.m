//
//  SceneDelegate.m
//  photoclear
//
//  Created by lifubing on 2021/6/15.
//

#import "SceneDelegate.h"
#import "PhotoTools.h"
#import "photoclear-Swift.h"
#import "NotificationManager.h"
#import "ScrollViewController.h"
#import "Preferences.h"
#import "OnboardingViewController.h"

@interface SceneDelegate () <OnboardingViewControllerDelegate>

@property (nonatomic, strong) OnboardingViewController *onboardingViewController;

@end

@implementation SceneDelegate


- (void)scene:(UIScene *)scene willConnectToSession:(UISceneSession *)session options:(UISceneConnectionOptions *)connectionOptions {
    // Use this method to optionally configure and attach the UIWindow `window` to the provided UIWindowScene `scene`.
    // If using a storyboard, the `window` property will automatically be initialized and attached to the scene.
    // This delegate does not imply the connecting scene or session are new (see `application:configurationForConnectingSceneSession` instead).

    // 检查是否需要显示引导页
    [self checkAndShowOnboardingIfNeeded];

    // 处理启动时的URL
    for (NSUserActivity *activity in connectionOptions.userActivities) {
        [self scene:scene continueUserActivity:activity];
    }

    // 处理URL Context
    for (UIOpenURLContext *urlContext in connectionOptions.URLContexts) {
        [self scene:scene openURLContexts:[NSSet setWithObject:urlContext]];
    }
}


- (void)sceneDidDisconnect:(UIScene *)scene {
    // Called as the scene is being released by the system.
    // This occurs shortly after the scene enters the background, or when its session is discarded.
    // Release any resources associated with this scene that can be re-created the next time the scene connects.
    // The scene may re-connect later, as its session was not necessarily discarded (see `application:didDiscardSceneSessions` instead).
}


- (void)sceneDidBecomeActive:(UIScene *)scene {
    // Called when the scene has moved from an inactive state to an active state.
    // Use this method to restart any tasks that were paused (or not yet started) when the scene was inactive.
    // 应用变为活跃状态时，检查是否需要更新照片缓存
    Preferences.sharedInstance.windowSize_Width = UIScreen.mainScreen.bounds.size.width;
    Preferences.sharedInstance.windowSize_Height = UIScreen.mainScreen.bounds.size.height;
    
    [self checkAndCachePhotosIfNeeded];
}

- (void)checkAndCachePhotosIfNeeded {
    
    [PhotoTools asyncCacheRandomPhotosForWidgetCompletion:^(BOOL success) {
        if (success) {
            // 通知Widget更新
            [WidgetKitHelper reloadAllWidgets];
            NSLog(@"Photo cache updated successfully");
        } else {
            NSLog(@"Photo cache update failed");
        }
    }];
}


- (void)sceneWillResignActive:(UIScene *)scene {
    // Called when the scene will move from an active state to an inactive state.
    // This may occur due to temporary interruptions (ex. an incoming phone call).
}


- (void)sceneWillEnterForeground:(UIScene *)scene {
    // Called as the scene transitions from the background to the foreground.
    // Use this method to undo the changes made on entering the background.
}


- (void)sceneDidEnterBackground:(UIScene *)scene {
    // Called as the scene transitions from the foreground to the background.
    // Use this method to save data, release shared resources, and store enough scene-specific state information
    // to restore the scene back to its current state.
    
//    [[NotificationManager sharedManager] sendNotificationWithTitle:@"PhotoClear" body:@"测试"];
}

#pragma mark - URL Handling

- (void)scene:(UIScene *)scene openURLContexts:(NSSet<UIOpenURLContext *> *)URLContexts {
    for (UIOpenURLContext *context in URLContexts) {
        NSURL *url = context.URL;
        [self handleURL:url];
    }
}

- (void)scene:(UIScene *)scene continueUserActivity:(NSUserActivity *)userActivity {
    if ([userActivity.activityType isEqualToString:NSUserActivityTypeBrowsingWeb]) {
        NSURL *url = userActivity.webpageURL;
        [self handleURL:url];
    }
}

- (void)handleURL:(NSURL *)url {
    if (!url) return;
    
    NSLog(@"Received URL: %@", url.absoluteString);
    
    // 检查是否是我们的URL Scheme
    if ([url.scheme isEqualToString:@"photoclear"]) {
        if ([url.host isEqualToString:@"openPhoto"]) {
            // 解析照片ID参数
            NSURLComponents *components = [NSURLComponents componentsWithURL:url resolvingAgainstBaseURL:NO];
            NSString *photoIdentifier = nil;
            
            for (NSURLQueryItem *queryItem in components.queryItems) {
                if ([queryItem.name isEqualToString:@"id"]) {
                    photoIdentifier = queryItem.value;
                    break;
                }
            }
            
            if (photoIdentifier && photoIdentifier.length > 0) {
                // 发送通知给相关界面
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                    
                    
                    [self navigateToScrollViewControllerWithPhotoIdentifier:photoIdentifier];
//                    [[NSNotificationCenter defaultCenter] postNotificationName:@"ShowPhotoFromWidget"
//                                                                        object:nil
//                                                                      userInfo:@{@"photoIdentifier": photoIdentifier}];
//                    
//                    NSLog(@"Handling photo identifier from URL: %@", photoIdentifier);
                });
            }
        }
    }
}

//- (void)navigateToScrollViewControllerWithPhotoIdentifier:(NSString *)photoIdentifier {
    
    // 先从当前的navigationController堆栈中找一下有没有ScrollViewController，如果没有创建一个ScrollViewController，push到vc堆栈中
    // 创建ScrollViewController并传递照片标识符
//    UIStoryboard *storyboard = [UIStoryboard storyboardWithName:@"Main" bundle:nil];
//    
//    
//    
//    // 尝试从storyboard获取ScrollViewController
//    ScrollViewController *scrollVC = nil;
//    @try {
//        scrollVC = [storyboard instantiateViewControllerWithIdentifier:@"ScrollViewController"];
//    } @catch (NSException *exception) {
//        // 如果storyboard中没有，直接创建
//        scrollVC = [[ScrollViewController alloc] init];
//    }
//    
//    if (scrollVC) {
//        // 设置照片数据
//        scrollVC.photoAssetArray = [PhotoTools allPhotos];
//        
//        // 将照片标识符保存到共享存储中，供ScrollViewController读取
//        NSUserDefaults *defaults = [[NSUserDefaults alloc] initWithSuiteName:@"group.com.lfb.manager.photoclear.shared"];
//        [defaults setObject:photoIdentifier forKey:@"selectedPhotoIdentifier"];
//        [defaults synchronize];
//        
//        // 跳转到ScrollViewController
//        [self.navigationController pushViewController:scrollVC animated:YES];
//        
//        NSLog(@"Navigating to ScrollViewController with photo identifier: %@", photoIdentifier);
//    }
//}

- (void)navigateToScrollViewControllerWithPhotoIdentifier:(NSString *)photoIdentifier {
    // 获取当前的根视图控制器
    UIViewController *rootViewController = self.window.rootViewController;
    
    // 如果根视图控制器是 UINavigationController
    if ([rootViewController isKindOfClass:[UINavigationController class]]) {
        UINavigationController *navigationController = (UINavigationController *)rootViewController;
        
        // 先从当前的 navigationController 堆栈中找一下有没有 ScrollViewController
        ScrollViewController *existingScrollVC = nil;
        for (UIViewController *vc in navigationController.viewControllers) {
            if ([vc isKindOfClass:[ScrollViewController class]]) {
                existingScrollVC = (ScrollViewController *)vc;
                break;
            }
        }
        
        // 如果找到了 ScrollViewController，就直接跳转
        if (existingScrollVC) {
            // 如果已经存在 ScrollViewController，传递照片标识符并跳转
            [existingScrollVC scrollToPhotoWithIdentifier:photoIdentifier];
            [navigationController popToViewController:existingScrollVC animated:NO];
        } else {
            // 如果没有找到，创建一个新的 ScrollViewController
            UIStoryboard *storyboard = [UIStoryboard storyboardWithName:@"Main" bundle:nil];
            ScrollViewController *scrollVC = [storyboard instantiateViewControllerWithIdentifier:@"ScrollViewController"];
//            ScrollViewController *scrollVC = [[ScrollViewController alloc] init];
            
            // 设置照片数据
            scrollVC.photoAssetArray = [PhotoTools allPhotos];
            
            // 将照片标识符保存到共享存储中，供 ScrollViewController 读取
            // 跳转到 ScrollViewController
            [navigationController pushViewController:scrollVC animated:NO];
            
            [scrollVC scrollToPhotoWithIdentifier:photoIdentifier];

            
            NSLog(@"Navigating to ScrollViewController with photo identifier: %@", photoIdentifier);
        }
    } else {
        NSLog(@"Root view controller is not a UINavigationController");
    }
}

#pragma mark - Onboarding

- (void)checkAndShowOnboardingIfNeeded {
    BOOL onboardingCompleted = [[NSUserDefaults standardUserDefaults] boolForKey:@"OnboardingCompleted"];

    // 测试用：强制显示引导页（在正式版本中应该删除这行）
    // onboardingCompleted = NO;

    if (!onboardingCompleted) {
        // 延迟显示引导页，确保主界面已经加载完成
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [self showOnboarding];
        });
    }
}

- (void)showOnboarding {
    if (self.onboardingViewController) {
        return; // 已经在显示引导页
    }

    self.onboardingViewController = [[OnboardingViewController alloc] init];
    self.onboardingViewController.delegate = self;
    self.onboardingViewController.modalPresentationStyle = UIModalPresentationFullScreen;

    UIViewController *rootViewController = self.window.rootViewController;
    if (rootViewController) {
        [rootViewController presentViewController:self.onboardingViewController animated:YES completion:nil];
    }
}

#pragma mark - OnboardingViewControllerDelegate

- (void)onboardingDidComplete {
    [self.onboardingViewController dismissViewControllerAnimated:YES completion:^{
        self.onboardingViewController = nil;

        // 引导页完成后，可以执行一些初始化操作
        [self checkAndCachePhotosIfNeeded];
    }];
}

@end
