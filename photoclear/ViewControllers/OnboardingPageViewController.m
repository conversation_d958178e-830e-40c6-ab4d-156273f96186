//
//  OnboardingPageViewController.m
//  photoclear
//
//  Created by lifubing on 2025/9/29.
//

#import "OnboardingPageViewController.h"

@interface OnboardingPageViewController ()

@property (nonatomic, strong) UIView *gradientView;
@property (nonatomic, strong) CAGradientLayer *gradientLayer;
@property (nonatomic, strong) UIImageView *iconImageView;
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UILabel *subtitleLabel;
@property (nonatomic, strong) UILabel *descriptionLabel;

@end

@implementation OnboardingPageViewController

- (instancetype)initWithPageType:(OnboardingPageType)pageType {
    self = [super init];
    if (self) {
        _pageType = pageType;
    }
    return self;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    [self setupUI];
    [self configureForPageType];
}

- (void)viewDidLayoutSubviews {
    [super viewDidLayoutSubviews];
    self.gradientLayer.frame = self.view.bounds;
}

- (void)setupUI {
    // 创建渐变背景
    self.gradientView = [[UIView alloc] init];
    [self.view addSubview:self.gradientView];
    
    self.gradientLayer = [CAGradientLayer layer];
    [self.gradientView.layer addSublayer:self.gradientLayer];
    
    // 创建图标
    self.iconImageView = [[UIImageView alloc] init];
    self.iconImageView.contentMode = UIViewContentModeScaleAspectFit;
    [self.view addSubview:self.iconImageView];
    
    // 创建标题
    self.titleLabel = [[UILabel alloc] init];
    self.titleLabel.textAlignment = NSTextAlignmentCenter;
    self.titleLabel.textColor = [UIColor whiteColor];
    self.titleLabel.font = [UIFont boldSystemFontOfSize:28];
    self.titleLabel.numberOfLines = 0;
    [self.view addSubview:self.titleLabel];
    
    // 创建副标题
    self.subtitleLabel = [[UILabel alloc] init];
    self.subtitleLabel.textAlignment = NSTextAlignmentCenter;
    self.subtitleLabel.textColor = [UIColor whiteColor];
    self.subtitleLabel.font = [UIFont boldSystemFontOfSize:20];
    self.subtitleLabel.numberOfLines = 0;
    [self.view addSubview:self.subtitleLabel];
    
    // 创建描述文本
    self.descriptionLabel = [[UILabel alloc] init];
    self.descriptionLabel.textAlignment = NSTextAlignmentCenter;
    self.descriptionLabel.textColor = [UIColor colorWithWhite:1.0 alpha:0.8];
    self.descriptionLabel.font = [UIFont systemFontOfSize:16];
    self.descriptionLabel.numberOfLines = 0;
    [self.view addSubview:self.descriptionLabel];
    
    [self setupConstraints];
}

- (void)setupConstraints {
    self.gradientView.translatesAutoresizingMaskIntoConstraints = NO;
    self.iconImageView.translatesAutoresizingMaskIntoConstraints = NO;
    self.titleLabel.translatesAutoresizingMaskIntoConstraints = NO;
    self.subtitleLabel.translatesAutoresizingMaskIntoConstraints = NO;
    self.descriptionLabel.translatesAutoresizingMaskIntoConstraints = NO;
    
    [NSLayoutConstraint activateConstraints:@[
        // 渐变背景
        [self.gradientView.topAnchor constraintEqualToAnchor:self.view.topAnchor],
        [self.gradientView.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor],
        [self.gradientView.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor],
        [self.gradientView.bottomAnchor constraintEqualToAnchor:self.view.bottomAnchor],
        
        // 图标
        [self.iconImageView.centerXAnchor constraintEqualToAnchor:self.view.centerXAnchor],
        [self.iconImageView.centerYAnchor constraintEqualToAnchor:self.view.centerYAnchor constant:-100],
        [self.iconImageView.widthAnchor constraintEqualToConstant:120],
        [self.iconImageView.heightAnchor constraintEqualToConstant:120],
        
        // 标题
        [self.titleLabel.topAnchor constraintEqualToAnchor:self.iconImageView.bottomAnchor constant:40],
        [self.titleLabel.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor constant:40],
        [self.titleLabel.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor constant:-40],
        
        // 副标题
        [self.subtitleLabel.topAnchor constraintEqualToAnchor:self.titleLabel.bottomAnchor constant:20],
        [self.subtitleLabel.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor constant:40],
        [self.subtitleLabel.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor constant:-40],
        
        // 描述
        [self.descriptionLabel.topAnchor constraintEqualToAnchor:self.subtitleLabel.bottomAnchor constant:20],
        [self.descriptionLabel.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor constant:40],
        [self.descriptionLabel.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor constant:-40]
    ]];
}

- (void)configureForPageType {
    switch (self.pageType) {
        case OnboardingPageTypeWidget:
            [self configureWidgetPage];
            break;
        case OnboardingPageTypeAutoPlay:
            [self configureAutoPlayPage];
            break;
        case OnboardingPageTypeNoAds:
            [self configureNoAdsPage];
            break;
        case OnboardingPageTypePermission:
            [self configurePermissionPage];
            break;
    }
}

- (void)configureWidgetPage {
    // 蓝色渐变背景
    self.gradientLayer.colors = @[
        (id)[UIColor colorWithRed:0.2 green:0.4 blue:1.0 alpha:1.0].CGColor,
        (id)[UIColor colorWithRed:0.1 green:0.2 blue:0.5 alpha:1.0].CGColor
    ];
    self.gradientLayer.startPoint = CGPointMake(0.5, 0);
    self.gradientLayer.endPoint = CGPointMake(0.5, 1);
    
    self.iconImageView.image = [UIImage imageNamed:@"icon_widget"];
    self.titleLabel.text = @"从掌控单开始，";
    self.subtitleLabel.text = @"重塑你的生活。";
    self.descriptionLabel.text = @"利用碎片化时间，通过Widget在桌面上快速整理照片，让每一刻都变得高效有序。";
}

- (void)configureAutoPlayPage {
    // 橙色渐变背景
    self.gradientLayer.colors = @[
        (id)[UIColor colorWithRed:1.0 green:0.6 blue:0.2 alpha:1.0].CGColor,
        (id)[UIColor colorWithRed:0.8 green:0.3 blue:0.1 alpha:1.0].CGColor
    ];
    self.gradientLayer.startPoint = CGPointMake(0.5, 0);
    self.gradientLayer.endPoint = CGPointMake(0.5, 1);
    
    self.iconImageView.image = [UIImage imageNamed:@"icon_list"];
    self.titleLabel.text = @"从掌控身体开始，";
    self.subtitleLabel.text = @"重塑你的生活。";
    self.descriptionLabel.text = @"照片自动轮播，只需要轻轻一点即可将照片进行整理，让管理变得简单高效。";
}

- (void)configureNoAdsPage {
    // 绿色渐变背景
    self.gradientLayer.colors = @[
        (id)[UIColor colorWithRed:0.4 green:0.8 blue:0.2 alpha:1.0].CGColor,
        (id)[UIColor colorWithRed:0.2 green:0.6 blue:0.1 alpha:1.0].CGColor
    ];
    self.gradientLayer.startPoint = CGPointMake(0.5, 0);
    self.gradientLayer.endPoint = CGPointMake(0.5, 1);
    
    self.iconImageView.image = [UIImage imageNamed:@"icon_config"];
    self.titleLabel.text = @"从掌控菜单开始，";
    self.subtitleLabel.text = @"重塑你的生活。";
    self.descriptionLabel.text = @"完全无广告，专注于为您提供纯净的照片整理体验，没有任何干扰。";
}

- (void)configurePermissionPage {
    // 紫色渐变背景
    self.gradientLayer.colors = @[
        (id)[UIColor colorWithRed:0.6 green:0.3 blue:1.0 alpha:1.0].CGColor,
        (id)[UIColor colorWithRed:0.4 green:0.1 blue:0.8 alpha:1.0].CGColor
    ];
    self.gradientLayer.startPoint = CGPointMake(0.5, 0);
    self.gradientLayer.endPoint = CGPointMake(0.5, 1);
    
    self.iconImageView.image = [UIImage imageNamed:@"icon_album"];
    self.titleLabel.text = @"访问您的照片";
    self.subtitleLabel.text = @"开始整理之旅";
    self.descriptionLabel.text = @"为了帮您整理照片，我们需要访问您的相册。您的隐私安全是我们的首要考虑。";
}

@end
