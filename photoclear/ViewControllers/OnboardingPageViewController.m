//
//  OnboardingPageViewController.m
//  photoclear
//
//  Created by lifubing on 2025/9/29.
//

#import "OnboardingPageViewController.h"

@interface OnboardingPageViewController ()

@property (nonatomic, strong) UIView *gradientView;
@property (nonatomic, strong) CAGradientLayer *gradientLayer;
@property (nonatomic, strong) UIImageView *iconImageView;
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UILabel *subtitleLabel;
@property (nonatomic, strong) UILabel *descriptionLabel;

@end

@implementation OnboardingPageViewController

- (instancetype)initWithPageType:(OnboardingPageType)pageType {
    self = [super init];
    if (self) {
        _pageType = pageType;
    }
    return self;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    [self setupUI];
    [self configureForPageType];
}

- (void)viewDidLayoutSubviews {
    [super viewDidLayoutSubviews];
    self.gradientLayer.frame = self.view.bounds;
}

- (void)setupUI {
    // 创建渐变背景
    self.gradientView = [[UIView alloc] init];
    [self.view addSubview:self.gradientView];

    self.gradientLayer = [CAGradientLayer layer];
    [self.gradientView.layer addSublayer:self.gradientLayer];

    // 创建Logo标签（替代图标）
    self.iconImageView = [[UIImageView alloc] init];
    self.iconImageView.contentMode = UIViewContentModeScaleAspectFit;
    self.iconImageView.backgroundColor = [UIColor whiteColor];
    self.iconImageView.layer.cornerRadius = 8;
    self.iconImageView.clipsToBounds = YES;
    [self.view addSubview:self.iconImageView];

    // 创建主标题 - 更大的字体
    self.titleLabel = [[UILabel alloc] init];
    self.titleLabel.textAlignment = NSTextAlignmentLeft;
    self.titleLabel.textColor = [UIColor whiteColor];
    self.titleLabel.font = [UIFont boldSystemFontOfSize:32];
    self.titleLabel.numberOfLines = 0;
    [self.view addSubview:self.titleLabel];

    // 创建副标题 - 更大的字体
    self.subtitleLabel = [[UILabel alloc] init];
    self.subtitleLabel.textAlignment = NSTextAlignmentLeft;
    self.subtitleLabel.textColor = [UIColor whiteColor];
    self.subtitleLabel.font = [UIFont boldSystemFontOfSize:32];
    self.subtitleLabel.numberOfLines = 0;
    [self.view addSubview:self.subtitleLabel];

    // 创建描述文本 - 更小更淡的字体
    self.descriptionLabel = [[UILabel alloc] init];
    self.descriptionLabel.textAlignment = NSTextAlignmentLeft;
    self.descriptionLabel.textColor = [UIColor colorWithWhite:1.0 alpha:0.7];
    self.descriptionLabel.font = [UIFont systemFontOfSize:14];
    self.descriptionLabel.numberOfLines = 0;
    [self.view addSubview:self.descriptionLabel];

    [self setupConstraints];
}

- (void)setupConstraints {
    self.gradientView.translatesAutoresizingMaskIntoConstraints = NO;
    self.iconImageView.translatesAutoresizingMaskIntoConstraints = NO;
    self.titleLabel.translatesAutoresizingMaskIntoConstraints = NO;
    self.subtitleLabel.translatesAutoresizingMaskIntoConstraints = NO;
    self.descriptionLabel.translatesAutoresizingMaskIntoConstraints = NO;
    
    [NSLayoutConstraint activateConstraints:@[
        // 渐变背景
        [self.gradientView.topAnchor constraintEqualToAnchor:self.view.topAnchor],
        [self.gradientView.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor],
        [self.gradientView.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor],
        [self.gradientView.bottomAnchor constraintEqualToAnchor:self.view.bottomAnchor],

        // Logo - 左上角位置，更小的尺寸
        [self.iconImageView.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor constant:32],
        [self.iconImageView.topAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.topAnchor constant:60],
        [self.iconImageView.widthAnchor constraintEqualToConstant:40],
        [self.iconImageView.heightAnchor constraintEqualToConstant:40],

        // 标题 - 居中偏上位置，左对齐
        [self.titleLabel.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor constant:32],
        [self.titleLabel.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor constant:-32],
        [self.titleLabel.centerYAnchor constraintEqualToAnchor:self.view.centerYAnchor constant:-60],

        // 副标题 - 紧跟标题，左对齐
        [self.subtitleLabel.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor constant:32],
        [self.subtitleLabel.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor constant:-32],
        [self.subtitleLabel.topAnchor constraintEqualToAnchor:self.titleLabel.bottomAnchor constant:4],

        // 描述 - 在副标题下方，左对齐
        [self.descriptionLabel.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor constant:32],
        [self.descriptionLabel.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor constant:-32],
        [self.descriptionLabel.topAnchor constraintEqualToAnchor:self.subtitleLabel.bottomAnchor constant:24]
    ]];
}

- (void)configureForPageType {
    switch (self.pageType) {
        case OnboardingPageTypeWidget:
            [self configureWidgetPage];
            break;
        case OnboardingPageTypeAutoPlay:
            [self configureAutoPlayPage];
            break;
        case OnboardingPageTypeNoAds:
            [self configureNoAdsPage];
            break;
        case OnboardingPageTypePermission:
            [self configurePermissionPage];
            break;
    }
}

- (void)configureWidgetPage {
    // 蓝色渐变背景 - 从浅蓝到深蓝
    self.gradientLayer.colors = @[
        (id)[UIColor colorWithRed:0.4 green:0.7 blue:1.0 alpha:1.0].CGColor,
        (id)[UIColor colorWithRed:0.1 green:0.2 blue:0.6 alpha:1.0].CGColor
    ];
    self.gradientLayer.startPoint = CGPointMake(0.5, 0);
    self.gradientLayer.endPoint = CGPointMake(0.5, 1);

    // Logo - 白色方块
    self.iconImageView.image = nil;
    self.iconImageView.backgroundColor = [UIColor whiteColor];

    // 文本内容 - 匹配视觉稿
    self.titleLabel.text = @"从掌控账单开始，";
    self.subtitleLabel.text = @"重塑你的生活。";
    self.descriptionLabel.text = @"轻松记录账单，整合各方平台数据\n清晰明了，便捷好用";
}

- (void)configureAutoPlayPage {
    // 橙色渐变背景
    self.gradientLayer.colors = @[
        (id)[UIColor colorWithRed:1.0 green:0.6 blue:0.2 alpha:1.0].CGColor,
        (id)[UIColor colorWithRed:0.8 green:0.3 blue:0.1 alpha:1.0].CGColor
    ];
    self.gradientLayer.startPoint = CGPointMake(0.5, 0);
    self.gradientLayer.endPoint = CGPointMake(0.5, 1);

    // Logo - 白色方块
    self.iconImageView.image = nil;
    self.iconImageView.backgroundColor = [UIColor whiteColor];

    self.titleLabel.text = @"从掌控身体开始，";
    self.subtitleLabel.text = @"重塑你的生活。";
    self.descriptionLabel.text = @"照片自动轮播，只需要轻轻一点即可将照片进行整理\n让管理变得简单高效";
}

- (void)configureNoAdsPage {
    // 绿色渐变背景
    self.gradientLayer.colors = @[
        (id)[UIColor colorWithRed:0.4 green:0.8 blue:0.2 alpha:1.0].CGColor,
        (id)[UIColor colorWithRed:0.2 green:0.6 blue:0.1 alpha:1.0].CGColor
    ];
    self.gradientLayer.startPoint = CGPointMake(0.5, 0);
    self.gradientLayer.endPoint = CGPointMake(0.5, 1);

    // Logo - 白色方块
    self.iconImageView.image = nil;
    self.iconImageView.backgroundColor = [UIColor whiteColor];

    self.titleLabel.text = @"从掌控菜单开始，";
    self.subtitleLabel.text = @"重塑你的生活。";
    self.descriptionLabel.text = @"完全无广告，专注于为您提供纯净的照片整理体验\n没有任何干扰";
}

- (void)configurePermissionPage {
    // 紫色渐变背景
    self.gradientLayer.colors = @[
        (id)[UIColor colorWithRed:0.6 green:0.3 blue:1.0 alpha:1.0].CGColor,
        (id)[UIColor colorWithRed:0.4 green:0.1 blue:0.8 alpha:1.0].CGColor
    ];
    self.gradientLayer.startPoint = CGPointMake(0.5, 0);
    self.gradientLayer.endPoint = CGPointMake(0.5, 1);

    // Logo - 白色方块
    self.iconImageView.image = nil;
    self.iconImageView.backgroundColor = [UIColor whiteColor];

    self.titleLabel.text = @"从掌控相册开始，";
    self.subtitleLabel.text = @"重塑你的生活。";
    self.descriptionLabel.text = @"为了帮您整理照片，我们需要访问您的相册\n您的隐私安全是我们的首要考虑";
}

@end
