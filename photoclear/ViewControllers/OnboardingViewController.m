//
//  OnboardingViewController.m
//  photoclear
//
//  Created by lifubing on 2025/9/29.
//

#import "OnboardingViewController.h"
#import "OnboardingPageViewController.h"
#import "PhotoTools.h"
#import "BaseRadiusButton.h"

@interface OnboardingViewController () <UIPageViewControllerDataSource, UIPageViewControllerDelegate>

@property (nonatomic, strong) UIPageViewController *pageViewController;
@property (nonatomic, strong) NSArray<OnboardingPageViewController *> *pages;
@property (nonatomic, strong) UIPageControl *pageControl;
@property (nonatomic, strong) BaseRadiusButton *nextButton;
@property (nonatomic, strong) UIButton *skipButton;
@property (nonatomic, assign) NSInteger currentPageIndex;

@end

@implementation OnboardingViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    [self setupUI];
    [self setupPages];
    [self setupPageViewController];
}

- (void)setupUI {
    self.view.backgroundColor = [UIColor blackColor];
    
    // 设置状态栏样式
    [self setNeedsStatusBarAppearanceUpdate];
    
    // 创建跳过按钮
    self.skipButton = [UIButton buttonWithType:UIButtonTypeSystem];
    [self.skipButton setTitle:@"跳过" forState:UIControlStateNormal];
    [self.skipButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    self.skipButton.titleLabel.font = [UIFont systemFontOfSize:16];
    [self.skipButton addTarget:self action:@selector(skipButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:self.skipButton];
    
    // 创建页面指示器
    self.pageControl = [[UIPageControl alloc] init];
    self.pageControl.numberOfPages = 4;
    self.pageControl.currentPage = 0;
    self.pageControl.pageIndicatorTintColor = [UIColor colorWithWhite:1.0 alpha:0.3];
    self.pageControl.currentPageIndicatorTintColor = [UIColor whiteColor];
    [self.view addSubview:self.pageControl];
    
    // 创建继续按钮 - 匹配视觉稿样式
    self.nextButton = [[BaseRadiusButton alloc] init];
    [self.nextButton setTitle:@"继续" forState:UIControlStateNormal];
    [self.nextButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    self.nextButton.titleLabel.font = [UIFont boldSystemFontOfSize:16];
    self.nextButton.backgroundColor = [UIColor colorWithRed:0.2 green:0.5 blue:1.0 alpha:1.0];
    self.nextButton.layer.cornerRadius = 25; // 更圆的按钮
    [self.nextButton addTarget:self action:@selector(nextButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:self.nextButton];
    
    [self setupConstraints];
}

- (void)setupConstraints {
    self.skipButton.translatesAutoresizingMaskIntoConstraints = NO;
    self.pageControl.translatesAutoresizingMaskIntoConstraints = NO;
    self.nextButton.translatesAutoresizingMaskIntoConstraints = NO;
    
    [NSLayoutConstraint activateConstraints:@[
        // 跳过按钮
        [self.skipButton.topAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.topAnchor constant:20],
        [self.skipButton.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor constant:-20],
        
        // 页面指示器
        [self.pageControl.centerXAnchor constraintEqualToAnchor:self.view.centerXAnchor],
        [self.pageControl.bottomAnchor constraintEqualToAnchor:self.nextButton.topAnchor constant:-30],
        
        // 继续按钮 - 更符合视觉稿的位置和尺寸
        [self.nextButton.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor constant:32],
        [self.nextButton.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor constant:-32],
        [self.nextButton.bottomAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.bottomAnchor constant:-50],
        [self.nextButton.heightAnchor constraintEqualToConstant:50]
    ]];
}

- (void)setupPages {
    // 创建4个引导页
    OnboardingPageViewController *page1 = [[OnboardingPageViewController alloc] initWithPageType:OnboardingPageTypeWidget];
    OnboardingPageViewController *page2 = [[OnboardingPageViewController alloc] initWithPageType:OnboardingPageTypeAutoPlay];
    OnboardingPageViewController *page3 = [[OnboardingPageViewController alloc] initWithPageType:OnboardingPageTypeNoAds];
    OnboardingPageViewController *page4 = [[OnboardingPageViewController alloc] initWithPageType:OnboardingPageTypePermission];
    
    self.pages = @[page1, page2, page3, page4];
}

- (void)setupPageViewController {
    self.pageViewController = [[UIPageViewController alloc] initWithTransitionStyle:UIPageViewControllerTransitionStyleScroll
                                                              navigationOrientation:UIPageViewControllerNavigationOrientationHorizontal
                                                                            options:nil];
    self.pageViewController.dataSource = self;
    self.pageViewController.delegate = self;
    
    [self.pageViewController setViewControllers:@[self.pages[0]]
                                      direction:UIPageViewControllerNavigationDirectionForward
                                       animated:NO
                                     completion:nil];
    
    [self addChildViewController:self.pageViewController];
    [self.view insertSubview:self.pageViewController.view atIndex:0];
    [self.pageViewController didMoveToParentViewController:self];
    
    // 设置页面控制器约束
    self.pageViewController.view.translatesAutoresizingMaskIntoConstraints = NO;
    [NSLayoutConstraint activateConstraints:@[
        [self.pageViewController.view.topAnchor constraintEqualToAnchor:self.view.topAnchor],
        [self.pageViewController.view.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor],
        [self.pageViewController.view.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor],
        [self.pageViewController.view.bottomAnchor constraintEqualToAnchor:self.view.bottomAnchor]
    ]];
}

#pragma mark - Actions

- (void)skipButtonTapped {
    [self completeOnboarding];
}

- (void)nextButtonTapped {
    if (self.currentPageIndex < self.pages.count - 1) {
        // 下一页
        self.currentPageIndex++;
        [self.pageViewController setViewControllers:@[self.pages[self.currentPageIndex]]
                                          direction:UIPageViewControllerNavigationDirectionForward
                                           animated:YES
                                         completion:nil];
        [self updateUI];
    } else {
        // 最后一页，处理权限申请或完成引导
        if (self.currentPageIndex == 3) {
            [self requestPhotoPermission];
        }
    }
}

- (void)requestPhotoPermission {
    [PHPhotoLibrary requestAuthorization:^(PHAuthorizationStatus status) {
        dispatch_async(dispatch_get_main_queue(), ^{
            [self completeOnboarding];
        });
    }];
}

- (void)completeOnboarding {
    // 标记引导页已完成
    [[NSUserDefaults standardUserDefaults] setBool:YES forKey:@"OnboardingCompleted"];
    [[NSUserDefaults standardUserDefaults] synchronize];
    
    if ([self.delegate respondsToSelector:@selector(onboardingDidComplete)]) {
        [self.delegate onboardingDidComplete];
    }
}

- (void)updateUI {
    self.pageControl.currentPage = self.currentPageIndex;
    
    if (self.currentPageIndex == self.pages.count - 1) {
        [self.nextButton setTitle:@"开始使用" forState:UIControlStateNormal];
    } else {
        [self.nextButton setTitle:@"下一步" forState:UIControlStateNormal];
    }
}

#pragma mark - UIPageViewControllerDataSource

- (UIViewController *)pageViewController:(UIPageViewController *)pageViewController viewControllerBeforeViewController:(UIViewController *)viewController {
    NSInteger index = [self.pages indexOfObject:(OnboardingPageViewController *)viewController];
    if (index == 0 || index == NSNotFound) {
        return nil;
    }
    return self.pages[index - 1];
}

- (UIViewController *)pageViewController:(UIPageViewController *)pageViewController viewControllerAfterViewController:(UIViewController *)viewController {
    NSInteger index = [self.pages indexOfObject:(OnboardingPageViewController *)viewController];
    if (index == NSNotFound || index == self.pages.count - 1) {
        return nil;
    }
    return self.pages[index + 1];
}

#pragma mark - UIPageViewControllerDelegate

- (void)pageViewController:(UIPageViewController *)pageViewController didFinishAnimating:(BOOL)finished previousViewControllers:(NSArray<UIViewController *> *)previousViewControllers transitionCompleted:(BOOL)completed {
    if (completed) {
        UIViewController *currentViewController = pageViewController.viewControllers.firstObject;
        self.currentPageIndex = [self.pages indexOfObject:(OnboardingPageViewController *)currentViewController];
        [self updateUI];
    }
}

#pragma mark - Status Bar

- (UIStatusBarStyle)preferredStatusBarStyle {
    return UIStatusBarStyleLightContent;
}

- (BOOL)prefersStatusBarHidden {
    return NO;
}

@end
